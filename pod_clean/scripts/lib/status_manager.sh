#!/bin/bash

# Pod状态管理模块
# 提供详细的Pod优化状态统计和显示功能

# 导入依赖
source "$(dirname "${BASH_SOURCE[0]}")/common.sh"
source "$(dirname "${BASH_SOURCE[0]}")/database.sh"

# 初始化Pod状态记录
init_pod_status() {
    local pod_name="$1"
    local pod_dir="$2"
    
    if [ ! -f "$DB_FILE" ]; then
        log_error "数据库文件不存在"
        return 1
    fi
    
    # 检查是否已存在记录
    local exists=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM pod_status_summary WHERE pod_name='$pod_name';" 2>/dev/null || echo "0")
    
    if [ "$exists" -eq 0 ]; then
        # 扫描当前文件状态
        cd "$pod_dir" || return 1
        local png_count=$(scan_pod_images "." "png" | wc -l)
        local jpg_count=$(scan_pod_images "." "jpg" | wc -l)
        local gif_count=$(scan_pod_images "." "gif" | wc -l)
        local total_count=$((png_count + jpg_count + gif_count))
        
        # 计算总大小
        local total_size=0
        if [ "$total_count" -gt 0 ]; then
            total_size=$(scan_pod_images "." "png jpg gif" | xargs -I {} stat -f%z "{}" 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
        fi
        cd - > /dev/null
        
        # 插入初始记录
        sqlite3 "$DB_FILE" << EOF
INSERT INTO pod_status_summary (
    pod_name, 
    original_png_count, original_jpg_count, original_gif_count, original_total_count, original_total_size,
    current_png_count, current_jpg_count, current_gif_count, current_total_count, current_total_size,
    last_scan_time
) VALUES (
    '$pod_name',
    $png_count, $jpg_count, $gif_count, $total_count, $total_size,
    $png_count, $jpg_count, $gif_count, $total_count, $total_size,
    CURRENT_TIMESTAMP
);
EOF
        log_info "初始化Pod状态: $pod_name (总计: $total_count 个文件)"
    fi
}

# 更新Pod当前状态
update_pod_current_status() {
    local pod_name="$1"
    local pod_dir="$2"
    
    if [ ! -f "$DB_FILE" ]; then
        log_error "数据库文件不存在"
        return 1
    fi
    
    # 扫描当前文件状态
    cd "$pod_dir" || return 1
    local png_count=$(scan_pod_images "." "png" | wc -l)
    local jpg_count=$(scan_pod_images "." "jpg" | wc -l)
    local gif_count=$(scan_pod_images "." "gif" | wc -l)
    local total_count=$((png_count + jpg_count + gif_count))
    
    # 计算当前总大小
    local total_size=0
    if [ "$total_count" -gt 0 ]; then
        total_size=$(scan_pod_images "." "png jpg gif" | xargs -I {} stat -f%z "{}" 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
    fi
    cd - > /dev/null
    
    # 更新当前状态
    sqlite3 "$DB_FILE" << EOF
UPDATE pod_status_summary SET
    current_png_count = $png_count,
    current_jpg_count = $jpg_count,
    current_gif_count = $gif_count,
    current_total_count = $total_count,
    current_total_size = $total_size,
    last_scan_time = CURRENT_TIMESTAMP
WHERE pod_name = '$pod_name';
EOF
}

# 记录优化操作详情
record_optimization_detail() {
    local pod_name="$1"
    local optimization_id="$2"
    local operation_type="$3"
    local files_affected="$4"
    local space_saved="$5"
    local compression_ratio="${6:-0}"
    local details="$7"
    
    if [ -f "$DB_FILE" ]; then
        sqlite3 "$DB_FILE" << EOF
INSERT INTO optimization_details (
    pod_name, optimization_id, operation_type, files_affected, 
    space_saved, compression_ratio, details
) VALUES (
    '$pod_name', $optimization_id, '$operation_type', $files_affected,
    $space_saved, $compression_ratio, '$details'
);
EOF
    fi
}

# 更新Pod优化统计
update_pod_optimization_stats() {
    local pod_name="$1"
    local optimization_type="$2"
    local files_count="$3"
    local space_saved="$4"
    
    if [ ! -f "$DB_FILE" ]; then
        return 1
    fi
    
    # 获取当前统计
    local current_stats=$(sqlite3 "$DB_FILE" "SELECT deleted_count, compressed_count, heic_converted_count, total_space_saved, optimization_types FROM pod_status_summary WHERE pod_name='$pod_name';" 2>/dev/null)
    
    if [ -n "$current_stats" ]; then
        IFS='|' read -r deleted_count compressed_count heic_converted_count total_space_saved optimization_types <<< "$current_stats"
        
        # 更新对应的计数器
        case "$optimization_type" in
            "delete")
                deleted_count=$((deleted_count + files_count))
                ;;
            "compress")
                compressed_count=$((compressed_count + files_count))
                ;;
            "heic")
                heic_converted_count=$((heic_converted_count + files_count))
                ;;
        esac
        
        # 更新总节省空间
        total_space_saved=$((total_space_saved + space_saved))
        
        # 更新优化类型列表
        if [[ "$optimization_types" != *"$optimization_type"* ]]; then
            if [ -n "$optimization_types" ]; then
                optimization_types="$optimization_types,$optimization_type"
            else
                optimization_types="$optimization_type"
            fi
        fi
        
        # 更新数据库
        sqlite3 "$DB_FILE" << EOF
UPDATE pod_status_summary SET
    deleted_count = $deleted_count,
    compressed_count = $compressed_count,
    heic_converted_count = $heic_converted_count,
    total_space_saved = $total_space_saved,
    optimization_types = '$optimization_types',
    last_optimization_time = CURRENT_TIMESTAMP
WHERE pod_name = '$pod_name';
EOF
    fi
}

# 格式化文件大小显示
format_size() {
    local size="$1"
    if [ "$size" -lt 1024 ]; then
        echo "${size}B"
    elif [ "$size" -lt 1048576 ]; then
        echo "$(echo "scale=1; $size/1024" | bc)KB"
    elif [ "$size" -lt 1073741824 ]; then
        echo "$(echo "scale=1; $size/1048576" | bc)MB"
    else
        echo "$(echo "scale=1; $size/1073741824" | bc)GB"
    fi
}

# 格式化时间显示
format_time() {
    local timestamp="$1"
    if [ -n "$timestamp" ]; then
        date -j -f "%Y-%m-%d %H:%M:%S" "$timestamp" "+%m-%d %H:%M" 2>/dev/null || echo "$timestamp"
    else
        echo "未知"
    fi
}

# 生成详细的Pod状态显示
get_detailed_pod_status() {
    local pod_name="$1"
    local pod_dir="$2"

    if [ ! -f "$DB_FILE" ]; then
        echo "❌ 未优化"
        return
    fi

    # 确保Pod状态已初始化（静默执行，避免输出干扰）
    init_pod_status "$pod_name" "$pod_dir" >/dev/null 2>&1

    # 更新当前状态（静默执行，避免输出干扰）
    update_pod_current_status "$pod_name" "$pod_dir" >/dev/null 2>&1

    # 获取详细统计信息
    local status_data=$(sqlite3 "$DB_FILE" << EOF
SELECT
    current_png_count, current_jpg_count, current_gif_count, current_total_count,
    original_total_count, deleted_count, compressed_count, heic_converted_count,
    total_space_saved, optimization_types, last_optimization_time
FROM pod_status_summary
WHERE pod_name='$pod_name';
EOF
)

    if [ -z "$status_data" ]; then
        echo "❌ 未优化"
        return
    fi

    IFS='|' read -r current_png current_jpg current_gif current_total \
                    original_total deleted_count compressed_count heic_count \
                    space_saved opt_types last_opt_time <<< "$status_data"

    # 构建优化状态部分
    local status_parts=()
    local has_optimization=false

    # 检查删除操作
    if [ "$deleted_count" -gt 0 ]; then
        local opt_time=$(format_time "$last_opt_time")
        status_parts+=("🗑️ 已删除${deleted_count}个")
        has_optimization=true
    fi

    # 检查压缩操作
    if [ "$compressed_count" -gt 0 ]; then
        local opt_time=$(format_time "$last_opt_time")
        status_parts+=("🗜️ 已压缩${compressed_count}个")
        has_optimization=true
    fi

    # 检查HEIC转换操作
    if [ "$heic_count" -gt 0 ]; then
        local opt_time=$(format_time "$last_opt_time")
        status_parts+=("🖼️ 已转HEIC${heic_count}个")
        has_optimization=true
    fi

    # 如果没有任何优化记录，检查是否有旧版本的优化记录
    if [ "$has_optimization" = "false" ]; then
        # 检查旧版本的optimizations表
        local old_opt_count=$(sqlite3 "$DB_FILE" "SELECT COUNT(*) FROM optimizations WHERE pod_name='$pod_name';" 2>/dev/null || echo "0")
        if [ "$old_opt_count" -gt 0 ]; then
            # 从旧表获取优化类型
            local old_opt_types=$(sqlite3 "$DB_FILE" "SELECT DISTINCT optimization_type FROM optimizations WHERE pod_name='$pod_name';" 2>/dev/null)
            while IFS= read -r opt_type; do
                case "$opt_type" in
                    "compress") status_parts+=("🗜️ 已压缩") ;;
                    "delete") status_parts+=("🗑️ 已删除") ;;
                    "heic") status_parts+=("🖼️ 已转HEIC") ;;
                    "mixed") status_parts+=("🔄  混合优化") ;;
                    *) status_parts+=("✅ 已优化") ;;
                esac
                has_optimization=true
            done <<< "$old_opt_types"
        fi
    fi

    # 如果仍然没有优化记录，显示未优化
    if [ "$has_optimization" = "false" ]; then
        echo "❌ 未优化"
        return
    fi

    # 添加总体信息
    if [ "$space_saved" -gt 0 ]; then
        local total_saved_fmt=$(format_size "$space_saved")
        local opt_time=$(format_time "$last_opt_time")
        status_parts+=("节省${total_saved_fmt}")
        if [ -n "$opt_time" ] && [ "$opt_time" != "未知" ]; then
            status_parts+=("${opt_time}")
        fi
    fi

    # 合并状态显示
    local status_display=$(printf '%s\n' "${status_parts[@]}" | tr '\n' ' | ' | sed 's/ | $//')

    echo "[$status_display]"
}

# 获取Pod优化摘要（用于快速显示）
get_pod_status_summary() {
    local pod_name="$1"

    if [ ! -f "$DB_FILE" ]; then
        echo "❌ 未优化"
        return
    fi

    local summary=$(sqlite3 "$DB_FILE" << EOF
SELECT
    deleted_count, compressed_count, heic_converted_count, total_space_saved,
    optimization_types, last_optimization_time
FROM pod_status_summary
WHERE pod_name='$pod_name';
EOF
)

    if [ -z "$summary" ]; then
        echo "❌ 未优化"
        return
    fi

    IFS='|' read -r deleted compressed heic space_saved opt_types last_time <<< "$summary"

    local icons=()
    [ "$deleted" -gt 0 ] && icons+=("🗑️")
    [ "$compressed" -gt 0 ] && icons+=("🗜️")
    [ "$heic" -gt 0 ] && icons+=("🖼️")

    if [ ${#icons[@]} -gt 1 ]; then
        icons=("🔄")  # 混合优化图标
    fi

    local icon_display=$(printf '%s' "${icons[@]}")
    local space_display=$(format_size "$space_saved")
    local time_display=$(format_time "$last_time")

    echo "${icon_display} 已优化 | 节省${space_display} | ${time_display}"
}

# 清理过期的状态缓存
cleanup_status_cache() {
    if [ -f "$DB_FILE" ]; then
        # 清理30天前的优化详情记录
        sqlite3 "$DB_FILE" "DELETE FROM optimization_details WHERE operation_time < datetime('now', '-30 days');"

        # 更新状态缓存
        sqlite3 "$DB_FILE" "UPDATE pod_status_summary SET status_cache = '' WHERE last_scan_time < datetime('now', '-1 day');"

        log_info "状态缓存清理完成"
    fi
}

# 批量更新Pod状态（性能优化版本）
batch_update_pod_status() {
    local pod_dirs=("$@")

    log_info "批量更新Pod状态..."

    for pod_info in "${pod_dirs[@]}"; do
        IFS=':' read -r pod_name pod_dir <<< "$pod_info"
        if [ -d "$pod_dir" ]; then
            # 添加随机延迟避免数据库锁定
            sleep "0.$(( RANDOM % 100 ))"
            init_pod_status "$pod_name" "$pod_dir" &

            # 限制并发数量为3以减少数据库锁定
            if (( $(jobs -r | wc -l) >= 3 )); then
                wait
            fi
        fi
    done

    wait  # 等待所有后台任务完成
    log_success "批量状态更新完成"
}
