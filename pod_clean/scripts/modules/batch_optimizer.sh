#!/bin/bash

# Pod库优化工具 - 批量优化模块
# 功能: 批量处理所有Pod库的优化任务，支持多种优化模式
# 用法: source "$SCRIPT_DIR/modules/batch_optimizer.sh"
# 依赖: common.sh, database.sh, image_processor.sh, unused_detector.sh
# 版本: v1.0

# Pod库优化功能 - 分为压缩和删除两种模式，支持增量优化
optimize_pod() {
    local pod_name="$1"
    local pod_dir="$2"
    local backup_dir="$3"
    local optimization_type="${4:-compress}"  # "compress" 或 "delete"

    log_info "开始优化 $pod_name (模式: $optimization_type)..."

    # 进入Pod目录
    cd "$pod_dir"

    local total_saved=0
    local processed_files=0

    if [ "$optimization_type" = "compress" ]; then
        # 压缩模式：优化图片质量
        compress_pod_images "$pod_name" "$backup_dir"
        # 使用全局变量获取结果，避免返回值限制
        total_saved=$LAST_COMPRESSION_SAVED
        processed_files=$LAST_COMPRESSION_COUNT
    elif [ "$optimization_type" = "delete" ]; then
        # 删除模式：删除重复和无引用图片
        delete_unused_images "$pod_name" "$backup_dir"
        # 使用全局变量获取结果，避免返回值限制
        total_saved=$LAST_DELETION_SAVED
        processed_files=$LAST_DELETION_COUNT
    elif [ "$optimization_type" = "mixed" ]; then
        # 混合模式：先删除无引用，再压缩
        log_info "$pod_name 执行混合优化（删除+压缩）..."

        # 第一步：删除无引用图片
        delete_unused_images "$pod_name" "$backup_dir"
        local delete_saved=$LAST_DELETION_SAVED
        local delete_count=$LAST_DELETION_COUNT

        # 第二步：压缩剩余图片
        compress_pod_images "$pod_name" "$backup_dir"
        local compress_saved=$LAST_COMPRESSION_SAVED
        local compress_count=$LAST_COMPRESSION_COUNT

        # 合并结果
        total_saved=$((delete_saved + compress_saved))
        processed_files=$((delete_count + compress_count))

        # 分别记录两次操作
        if [ $delete_saved -gt 0 ]; then
            record_optimization "$pod_name" "delete" "$delete_count" "$delete_saved" "$backup_dir"
        fi
        if [ $compress_saved -gt 0 ]; then
            record_optimization "$pod_name" "compress" "$compress_count" "$compress_saved" "$backup_dir"
        fi

        log_success "$pod_name 混合优化完成: 删除节省 $((delete_saved / 1024))KB, 压缩节省 $((compress_saved / 1024))KB"
    elif [ "$optimization_type" = "heic" ]; then
        # HEIC模式：先转HEIC，再对剩余PNG/JPG执行压缩
        convert_pod_images_to_heic "$pod_name" "$backup_dir"
        # 使用全局变量拿到准确统计
        total_saved=$LAST_OPTIMIZATION_SAVED
        processed_files=$LAST_OPTIMIZATION_COUNT

        # 记录一次"heic"聚合
        if [ $total_saved -gt 0 ]; then
            record_optimization "$pod_name" "heic" "$processed_files" "$total_saved" "$backup_dir"
            log_success "$pod_name HEIC转换完成: 节省 $((total_saved / 1024))KB"
        else
            log_info "$pod_name 没有可转换为HEIC的文件"
        fi

        # 对剩余的PNG/JPG继续压缩（作为独立一次操作记录）
        compress_pod_images "$pod_name" "$backup_dir"
        local compress_saved=$LAST_COMPRESSION_SAVED
        local compress_count=$LAST_COMPRESSION_COUNT
        if [ $compress_saved -gt 0 ]; then
            record_optimization "$pod_name" "compress" "$compress_count" "$compress_saved" "$backup_dir"
            log_success "$pod_name 压缩完成: 额外节省 $((compress_saved / 1024))KB"
        fi

        # heic模式已自行记录聚合，提前返回
        cd - > /dev/null
        return 0
    else
        log_error "未知的优化类型: $optimization_type"
        cd - > /dev/null
        return 1
    fi

    # 返回原目录
    cd - > /dev/null

    # 记录优化结果
    if [ $total_saved -gt 0 ]; then
        record_optimization "$pod_name" "$optimization_type" "$processed_files" "$total_saved" "$backup_dir"
        log_success "$pod_name 优化完成: 节省 $((total_saved / 1024))KB"
    else
        log_info "$pod_name 没有新的文件需要优化"
    fi

    return 0
}

# 全部优化所有Pod库 - 内部增量优化
batch_optimize_pods() {
    echo ""
    echo "选择优化类型:"
    echo "1. 压缩图片 (减少文件大小，保持文件)"
    echo "2. 删除图片 (仅删除无引用图片)"
    echo "3. 混合优化 (先删除无引用，再压缩)"
    echo "4. 转换为HEIC (先转HEIC，再压缩剩余PNG/JPG)"
    echo -n "请选择 [1/2/3/4]: "
    read -r opt_type

    local optimization_mode="compress"
    case "$opt_type" in
        1) optimization_mode="compress" ;;
        2) optimization_mode="delete" ;;
        3) optimization_mode="mixed" ;;
        4) optimization_mode="heic" ;;
        *)
            log_error "无效选择，使用默认压缩模式"
            optimization_mode="compress"
            ;;
    esac

    log_info "开始全部优化 (模式: $optimization_mode，内部增量优化)..."

    # 创建全局备份目录到统一的 backups 目录下
    mkdir -p "$BACKUP_ROOT"
    local global_backup="$BACKUP_ROOT/full_optimization_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$global_backup"
    echo "全局备份目录: $global_backup"
    echo ""

    local processed_count=0
    local total_pods=0

    # 统计总数
    total_pods=$(find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        if ! should_skip_pod_directory "$dir" "$name"; then
            echo "$name"
        fi
    done | wc -l)

    echo "发现 $total_pods 个Pod库，开始处理..."
    echo ""

    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 检查是否应该跳过此Pod目录（包括 .ignore_pod_clean 文件检查）
        if should_skip_pod_directory "$dir" "$name"; then
            continue
        fi

        processed_count=$((processed_count + 1))
        echo "[$processed_count/$total_pods] 处理 $name..."

        # 创建Pod专用备份目录
        local pod_backup="$global_backup/${name}"
        mkdir -p "$pod_backup"

        # 优化Pod库 - 内部会自动跳过已优化的文件
        if [ "$optimization_mode" = "mixed" ]; then
            # 混合模式：先删除，再压缩
            optimize_pod "$name" "$dir" "$pod_backup" "delete"
            optimize_pod "$name" "$dir" "$pod_backup" "compress"
        elif [ "$optimization_mode" = "heic" ]; then
            optimize_pod "$name" "$dir" "$pod_backup" "heic"
        else
            optimize_pod "$name" "$dir" "$pod_backup" "$optimization_mode"
        fi

        echo ""
    done

    log_success "全部优化完成: 处理了 $total_pods 个Pod库"
    echo "全局备份目录: $global_backup"
}

# 增量优化
incremental_optimization() {
    log_info "开始增量优化..."
    
    # 发现所有Pod库
    local pods=()
    local new_pods=()
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")

        # 检查是否应该跳过此Pod目录（包括 .ignore_pod_clean 文件检查）
        if should_skip_pod_directory "$dir" "$name"; then
            continue
        fi

        echo "$name"
    done > /tmp/all_pods.txt
    
    local total_pods=$(wc -l < /tmp/all_pods.txt)
    local new_count=0
    
    echo "发现 $total_pods 个Pod库，检查优化状态..."
    echo ""
    
    while read -r pod_name; do
        if ! is_pod_optimized "$pod_name"; then
            echo "  ✅ $pod_name (未优化)"
            new_count=$((new_count + 1))
        else
            echo "  ⏭️  $pod_name (已优化)"
        fi
    done < /tmp/all_pods.txt
    
    rm -f /tmp/all_pods.txt
    
    if [ $new_count -eq 0 ]; then
        log_success "所有Pod库均已优化，无需增量优化"
        return 0
    fi
    
    echo ""
    log_info "发现 $new_count 个未优化的Pod库"
    echo -n "是否执行增量优化? [y/N]: "
    read -r confirm
    
    if [[ "$confirm" =~ ^[Yy]$ ]]; then
        log_info "调用完整优化脚本进行增量处理..."
        "$SCRIPTS_DIR/complete_pod_optimizer.sh" --incremental
    fi
}
