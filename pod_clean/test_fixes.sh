#!/bin/bash

# 测试修复的功能
# 1. 测试 .ignore_pod_clean 文件跳过机制
# 2. 测试选择性优化状态显示修复

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_DIR="$SCRIPT_DIR/scripts/lib"

# 加载依赖库
source "$LIB_DIR/common.sh"
source "$LIB_DIR/database.sh"
source "$LIB_DIR/status_manager.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

test_info() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

test_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

test_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

test_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 测试 .ignore_pod_clean 文件跳过机制
test_ignore_file_mechanism() {
    test_info "测试 .ignore_pod_clean 文件跳过机制..."
    
    # 创建一个临时测试目录
    local test_dir="../test_pod_ignore"
    local test_podspec="$test_dir/test_pod_ignore.podspec"
    local ignore_file="$test_dir/.ignore_pod_clean"
    
    # 清理可能存在的测试目录
    rm -rf "$test_dir"
    
    # 创建测试Pod目录和文件
    mkdir -p "$test_dir"
    echo "Pod::Spec.new do |s|" > "$test_podspec"
    echo "  s.name = 'test_pod_ignore'" >> "$test_podspec"
    echo "end" >> "$test_podspec"
    
    # 测试没有 .ignore_pod_clean 文件时的行为
    if should_skip_pod_directory "$test_dir" "test_pod_ignore"; then
        test_error "没有 .ignore_pod_clean 文件时不应该跳过"
        rm -rf "$test_dir"
        return 1
    else
        test_success "没有 .ignore_pod_clean 文件时正确处理"
    fi
    
    # 创建 .ignore_pod_clean 文件
    echo "# 此Pod被标记为跳过优化" > "$ignore_file"
    echo "原因: 测试用途" >> "$ignore_file"
    
    # 测试有 .ignore_pod_clean 文件时的行为
    if should_skip_pod_directory "$test_dir" "test_pod_ignore"; then
        test_success "有 .ignore_pod_clean 文件时正确跳过"
    else
        test_error "有 .ignore_pod_clean 文件时应该跳过"
        rm -rf "$test_dir"
        return 1
    fi
    
    # 清理测试目录
    rm -rf "$test_dir"
    test_success ".ignore_pod_clean 文件跳过机制测试通过"
    return 0
}

# 测试状态显示功能
test_status_display() {
    test_info "测试Pod状态显示功能..."
    
    # 初始化数据库
    init_database >/dev/null 2>&1
    
    if [ ! -f "$DB_FILE" ]; then
        test_error "数据库文件不存在，无法测试状态显示"
        return 1
    fi
    
    # 测试未优化Pod的状态显示
    local test_pod="test_status_pod"
    local status=$(get_pod_optimization_status "$test_pod")
    
    if [[ "$status" == *"未优化"* ]]; then
        test_success "未优化Pod状态显示正确: $status"
    else
        test_warning "未优化Pod状态显示: $status"
    fi
    
    # 查找一个真实的Pod进行测试
    local real_pod=""
    find .. -maxdepth 2 -name "*.podspec" | head -1 | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        if ! should_skip_pod_directory "$dir" "$name"; then
            echo "$name:$dir"
            break
        fi
    done > /tmp/test_pod.txt
    
    if [ -s /tmp/test_pod.txt ]; then
        local pod_info=$(cat /tmp/test_pod.txt)
        IFS=':' read -r pod_name pod_dir <<< "$pod_info"
        
        test_info "测试真实Pod状态显示: $pod_name"
        
        # 测试详细状态显示
        local detailed_status=$(get_detailed_pod_status "$pod_name" "$pod_dir")
        echo "  详细状态: $detailed_status"
        
        # 测试状态摘要显示
        if declare -f get_pod_status_summary > /dev/null 2>&1; then
            local summary_status=$(get_pod_status_summary "$pod_name")
            echo "  摘要状态: $summary_status"
        fi
        
        test_success "Pod状态显示功能测试完成"
    else
        test_warning "未找到可测试的真实Pod"
    fi
    
    rm -f /tmp/test_pod.txt
    return 0
}

# 测试选择性优化Pod发现功能
test_selective_pod_discovery() {
    test_info "测试选择性优化Pod发现功能..."
    
    local pod_count=0
    local skipped_count=0
    
    find .. -maxdepth 2 -name "*.podspec" | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        
        if should_skip_pod_directory "$dir" "$name"; then
            echo "SKIPPED:$name"
        else
            echo "VALID:$name"
        fi
    done > /tmp/pod_discovery_test.txt
    
    pod_count=$(grep "^VALID:" /tmp/pod_discovery_test.txt | wc -l | tr -d ' ')
    skipped_count=$(grep "^SKIPPED:" /tmp/pod_discovery_test.txt | wc -l | tr -d ' ')
    
    echo "  发现有效Pod: $pod_count 个"
    echo "  跳过Pod: $skipped_count 个"
    
    if [ "$pod_count" -gt 0 ]; then
        test_success "Pod发现功能正常，发现 $pod_count 个有效Pod"
    else
        test_warning "未发现有效Pod"
    fi
    
    # 显示跳过的Pod（如果有）
    if [ "$skipped_count" -gt 0 ]; then
        echo "  跳过的Pod:"
        grep "^SKIPPED:" /tmp/pod_discovery_test.txt | sed 's/^SKIPPED:/    - /'
    fi
    
    rm -f /tmp/pod_discovery_test.txt
    return 0
}

# 创建演示 .ignore_pod_clean 文件
create_demo_ignore_file() {
    test_info "创建演示 .ignore_pod_clean 文件..."
    
    # 查找一个Pod目录作为演示
    find .. -maxdepth 2 -name "*.podspec" | head -1 | while read podspec; do
        local dir=$(dirname "$podspec")
        local name=$(basename "$dir")
        
        if ! should_skip_pod_directory "$dir" "$name"; then
            local demo_ignore_file="$dir/.ignore_pod_clean.demo"
            
            cat > "$demo_ignore_file" << 'EOF'
# Pod优化跳过标记文件
# 
# 此文件的存在会让Pod优化脚本跳过当前Pod目录
# 
# 使用方法:
#   1. 将此文件重命名为 .ignore_pod_clean
#   2. Pod优化脚本会自动跳过此目录
#   3. 删除此文件可恢复正常优化
#
# 适用场景:
#   - 包含重要图片资源，不希望被优化
#   - 正在开发中的Pod，暂时不需要优化
#   - 第三方Pod，不希望修改其资源文件
#   - 特殊用途的Pod，需要保持原始文件
#
# 创建时间: $(date)
# Pod名称: $name
EOF
            
            echo "  演示文件已创建: $demo_ignore_file"
            echo "  要启用跳过功能，请将文件重命名为: $dir/.ignore_pod_clean"
            break
        fi
    done
    
    test_success "演示文件创建完成"
}

# 主测试函数
run_tests() {
    echo "Pod优化脚本修复功能测试"
    echo "═══════════════════════════════════════════════════════════════"
    echo ""
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 测试列表
    local tests=(
        "test_ignore_file_mechanism"
        "test_status_display"
        "test_selective_pod_discovery"
    )
    
    # 运行测试
    for test_func in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        echo ""
        
        if $test_func; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
    done
    
    # 创建演示文件
    echo ""
    create_demo_ignore_file
    
    # 显示测试结果
    echo ""
    echo "═══════════════════════════════════════════════════════════════"
    echo "测试结果汇总:"
    echo "  总计测试: $total_tests"
    echo "  通过测试: $passed_tests"
    echo "  失败测试: $failed_tests"
    
    if [ $failed_tests -eq 0 ]; then
        test_success "所有测试通过！修复功能正常"
        echo ""
        echo "💡 修复功能说明:"
        echo "   1. .ignore_pod_clean 文件跳过机制已启用"
        echo "   2. 选择性优化状态显示问题已修复"
        echo "   3. 所有优化模式都会检查跳过标记文件"
        echo ""
        echo "🔧 使用方法:"
        echo "   - 在Pod目录下创建 .ignore_pod_clean 文件可跳过优化"
        echo "   - 选择性优化界面现在会正确显示Pod状态"
        echo "   - 状态信息从数据库实时读取，确保准确性"
        return 0
    else
        test_error "有 $failed_tests 个测试失败，请检查相关问题"
        return 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-}" in
        -h|--help)
            echo "Pod优化脚本修复功能测试工具"
            echo ""
            echo "用法:"
            echo "  $0              运行所有测试"
            echo "  $0 -h           显示帮助"
            ;;
        *)
            run_tests
            ;;
    esac
fi
